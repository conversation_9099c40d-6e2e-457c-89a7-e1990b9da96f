import { cn } from "@/lib/utils";
import { LanguageProvider } from "@/hooks/use-language";
import type { Metadata } from "next";
import { Poppins } from "next/font/google";
import type React from "react";
import "./globals.css";

const poppins = Poppins({
	subsets: ["latin"],
	weight: ["400", "500", "600", "700", "800", "900"],
	variable: "--font-poppins",
});

export const metadata: Metadata = {
	title: "UCI Gran Fondo Îles de Guadeloupe 2025",
	description:
		"Participez à l'épreuve officielle UCI Gran Fondo World Series en Guadeloupe les 6 et 7 décembre 2025. Une course cycliste amateur qualificative pour les Championnats du Monde 2026.",
	keywords: "UCI Gran Fondo, Guadeloupe, cyclisme, course cycliste, Gran Fondo World Series, 2025, cyclisme amateur",
	generator: "v0.dev",
};

export default function RootLayout({
	children,
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang="fr" className="!scroll-smooth">
			<body className={cn("min-h-screen bg-background font-sans antialiased", poppins.variable)}>
				<LanguageProvider>{children}</LanguageProvider>
			</body>
		</html>
	);
}
