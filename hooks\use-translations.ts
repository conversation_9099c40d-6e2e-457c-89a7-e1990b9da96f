"use client";

import enMessages from "../messages/en.json";
import frMessages from "../messages/fr.json";
import { useLanguage } from "./use-language";

type Messages = typeof frMessages;

export function useTranslations() {
	let language = "fr"; // Default fallback

	try {
		const languageContext = useLanguage();
		language = languageContext.language;
	} catch (error) {
		console.warn("LanguageProvider not available, using default language (fr)");
	}

	const messages: Messages = language === "fr" ? frMessages : enMessages;

	const t = (key: string): string => {
		try {
			const keys = key.split(".");
			let value: any = messages;

			for (const k of keys) {
				if (value && typeof value === "object" && k in value) {
					value = value[k];
				} else {
					console.warn(`Translation key not found: ${key}`);
					return key;
				}
			}

			return typeof value === "string" ? value : key;
		} catch (error) {
			console.error(`Translation error for key: ${key}`, error);
			return key;
		}
	};

	return { t, language };
}
